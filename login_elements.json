{"status": "success", "url": "http://localhost:9001/login", "found_elements": {"forms": {"count": 1, "elements": [{"index": 0, "tag": "form", "type": "", "name": "", "id": "", "class": "jss27", "placeholder": "", "value": "", "visible": true, "html": "<form class=\"jss27\" novalidate=\"\"><div class=\"MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-isbt42\"><div class=\"MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss33 css-15j76c0\"><div class=\"MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root jss35 css-feqhe6\"><div class=\"MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-adornedStart css-7lqkqc\"><div class=\"MuiInputAdornment-root MuiInputAdornment-positionStart MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1a6giau\"><span class=\"notranslate\">​</span><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"min-icon\" fill=\"currentcolor\" viewBox=\"0 0 9.008 12\"><defs><clipPath id=\"clip-path\"><rect id=\"Rectangle_991\" data-name=\"Rectangle 991\" width=\"9.008\" height=\"12\" fill=\"#071d43\"></rect></clipPath></defs><g id=\"Group_2365\" data-name=\"Group 2365\"><path id=\"Path_7088\" data-name=\"Path 7088\" d=\"M26.843,6.743a3.4,3.4,0,0,0,3.411-3.372,3.411,3.411,0,0,0-6.822,0,3.4,3.4,0,0,0,3.411,3.372\" transform=\"translate(-22.334)\" fill=\"#071d43\"></path><path id=\"Path_7089\" data-name=\"Path 7089\" d=\"M8.639,157.057a5.164,5.164,0,0,0-1.957-1.538,5.438,5.438,0,0,0-1.083-.362,5.2,5.2,0,0,0-1.117-.123c-.075,0-.151,0-.225.005H4.231a4.928,4.928,0,0,0-.549.059,5.236,5.236,0,0,0-3.276,1.92c-.029.039-.059.078-.086.116h0a1.723,1.723,0,0,0-.134,1.784,1.583,1.583,0,0,0,.255.356,1.559,1.559,0,0,0,.337.267,1.613,1.613,0,0,0,.4.167,1.742,1.742,0,0,0,.449.058H7.389a1.747,1.747,0,0,0,.452-.058,1.593,1.593,0,0,0,.4-.169,1.524,1.524,0,0,0,.335-.271,1.548,1.548,0,0,0,.251-.361,1.761,1.761,0,0,0-.191-1.85\" transform=\"translate(0.001 -147.766)\" fill=\"#071d43\"></path></g></svg></div><input aria-invalid=\"false\" autocomplete=\"username\" id=\"accessKey\" name=\"accessKey\" placeholder=\"Username\" type=\"text\" class=\"MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g\" value=\"\"><fieldset aria-hidden=\"true\" class=\"MuiOutlinedInput-notchedOutline css-igs3ac\"><legend class=\"css-ihdtdm\"><span class=\"notranslate\">​</span></legend></fieldset></div></div></div><div class=\"MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-15j76c0\"><div class=\"MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root jss35 css-feqhe6\"><div class=\"MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-adornedStart css-7lqkqc\"><div class=\"MuiInputAdornment-root MuiInputAdornment-positionStart MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1a6giau\"><span class=\"notranslate\">​</span><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"min-icon\" fill=\"currentcolor\" viewBox=\"0 0 12 12\"><path id=\"Path_7819\" data-name=\"Path 7819\" d=\"M9.884,3.523H8.537V2.27A2.417,2.417,0,0,0,6,0,2.417,2.417,0,0,0,3.463,2.27V3.523H2.116A2.019,2.019,0,0,0,0,5.423V9.413a2.012,2.012,0,0,0,2.062,1.9L6,12l3.938-.688A2.012,2.012,0,0,0,12,9.413V5.423a2.019,2.019,0,0,0-2.116-1.9M6.5,7.658v.724a.474.474,0,0,1-.472.474H5.971A.474.474,0,0,1,5.5,8.381V7.658a.9.9,0,0,1-.394-.744h0a.894.894,0,1,1,1.4.744m.985-4.135H4.514V2.27A1.416,1.416,0,0,1,6,.94,1.416,1.416,0,0,1,7.486,2.27Z\" fill=\"#071d43\"></path></svg></div><input aria-invalid=\"false\" autocomplete=\"current-password\" id=\"secretKey\" name=\"secretKey\" placeholder=\"Password\" type=\"password\" class=\"MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g\" value=\"\"><fieldset aria-hidden=\"true\" class=\"MuiOutlinedInput-notchedOutline css-igs3ac\"><legend class=\"css-ihdtdm\"><span class=\"notranslate\">​</span></legend></fieldset></div></div></div></div><div class=\"MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss29 css-1wxaqej\"><button disabled=\"\" label=\"Login\" type=\"submit\" color=\"primary\" id=\"do-login\" class=\"sc-bcXHqe cudOsJ jss28\"><span class=\"button-label\">Login</span></button></div><div class=\"MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss30 css-1wxaqej\"></div><div class=\"MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss30 css-1wxaqej\"><div class=\"MuiBox-root css-0\" style=\"text-align: center; margin-top: 20px;\"><span style=\"color: rgb(39, 129, 176); font: 14px Inter; text-decoration: underline; cursor: pointer;\">Use STS</span><span style=\"color: rgb(39, 129, 176); font: bold 12px / 15px Inter; text-decoration: none; padding-left: 4px;\">➔</span></div></div></form>"}]}, "username_inputs": {"count": 1, "elements": [{"index": 0, "tag": "input", "type": "text", "name": "accessKey", "id": "accessKey", "class": "MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g", "placeholder": "Username", "value": "", "visible": true, "html": "<input aria-invalid=\"false\" autocomplete=\"username\" id=\"accessKey\" name=\"accessKey\" placeholder=\"Username\" type=\"text\" class=\"MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g\" value=\"\">"}]}, "password_inputs": {"count": 1, "elements": [{"index": 0, "tag": "input", "type": "password", "name": "secret<PERSON>ey", "id": "secret<PERSON>ey", "class": "MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g", "placeholder": "Password", "value": "", "visible": true, "html": "<input aria-invalid=\"false\" autocomplete=\"current-password\" id=\"secretKey\" name=\"secretKey\" placeholder=\"Password\" type=\"password\" class=\"MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g\" value=\"\">"}]}, "submit_buttons": {"count": 1, "elements": [{"index": 0, "tag": "button", "type": "submit", "name": "", "id": "do-login", "class": "sc-bcXHqe cudOsJ jss28", "placeholder": "", "value": "", "visible": true, "html": "<button disabled=\"\" label=\"Login\" type=\"submit\" color=\"primary\" id=\"do-login\" class=\"sc-bcXHqe cudOsJ jss28\"><span class=\"button-label\">Login</span></button>"}]}, "all_inputs": {"count": 2, "elements": [{"index": 0, "tag": "input", "type": "text", "name": "accessKey", "id": "accessKey", "class": "MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g", "placeholder": "Username", "value": "", "visible": true, "html": "<input aria-invalid=\"false\" autocomplete=\"username\" id=\"accessKey\" name=\"accessKey\" placeholder=\"Username\" type=\"text\" class=\"MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g\" value=\"\">"}, {"index": 1, "tag": "input", "type": "password", "name": "secret<PERSON>ey", "id": "secret<PERSON>ey", "class": "MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g", "placeholder": "Password", "value": "", "visible": true, "html": "<input aria-invalid=\"false\" autocomplete=\"current-password\" id=\"secretKey\" name=\"secretKey\" placeholder=\"Password\" type=\"password\" class=\"MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g\" value=\"\">"}]}, "all_buttons": {"count": 1, "elements": [{"index": 0, "tag": "button", "type": "submit", "name": "", "id": "do-login", "class": "sc-bcXHqe cudOsJ jss28", "placeholder": "", "value": "", "visible": true, "html": "<button disabled=\"\" label=\"Login\" type=\"submit\" color=\"primary\" id=\"do-login\" class=\"sc-bcXHqe cudOsJ jss28\"><span class=\"button-label\">Login</span></button>"}]}}, "summary": {"total_forms": 1, "total_username_inputs": 1, "total_password_inputs": 1, "total_submit_buttons": 1, "total_inputs": 2, "total_buttons": 1}}