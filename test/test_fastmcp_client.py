#!/usr/bin/env python3
"""
Test client for FastMCP Playwright server.
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path


async def test_mcp_server():
    """Test the FastMCP Playwright server."""
    
    # Start the MCP server as a subprocess
    server_script = Path(__file__).parent / "playwright_mcp_server.py"
    
    print("Starting MCP server...")
    server_process = subprocess.Popen(
        [sys.executable, str(server_script)],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    try:
        # Send initialization request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print("Sending initialization request...")
        server_process.stdin.write(json.dumps(init_request) + "\n")
        server_process.stdin.flush()
        
        # Read response
        response_line = server_process.stdout.readline()
        print(f"Init response: {response_line.strip()}")
        
        # Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        server_process.stdin.flush()
        
        # List tools
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        print("Listing available tools...")
        server_process.stdin.write(json.dumps(list_tools_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        print(f"Tools list response: {response_line.strip()}")
        
        # Test load_page tool
        load_page_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "load_page",
                "arguments": {
                    "url": "http://localhost:9001/login",
                    "headless": True,
                    "timeout": 30000
                }
            }
        }
        
        print("Testing load_page tool with http://localhost:9001/login...")
        server_process.stdin.write(json.dumps(load_page_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        print(f"Load page response: {response_line.strip()}")
        
        # Test get_dom tool
        get_dom_request = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "get_dom",
                "arguments": {
                    "pretty_print": True
                }
            }
        }
        
        print("Testing get_dom tool...")
        server_process.stdin.write(json.dumps(get_dom_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        print(f"Get DOM response: {response_line.strip()}")
        
        # Test find_login_elements tool
        find_login_request = {
            "jsonrpc": "2.0",
            "id": 5,
            "method": "tools/call",
            "params": {
                "name": "find_login_elements",
                "arguments": {
                    "include_html": True
                }
            }
        }
        
        print("Testing find_login_elements tool...")
        server_process.stdin.write(json.dumps(find_login_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        print(f"Find login elements response: {response_line.strip()}")
        
        # Test close_browser tool
        close_browser_request = {
            "jsonrpc": "2.0",
            "id": 6,
            "method": "tools/call",
            "params": {
                "name": "close_browser",
                "arguments": {}
            }
        }
        
        print("Testing close_browser tool...")
        server_process.stdin.write(json.dumps(close_browser_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        print(f"Close browser response: {response_line.strip()}")
        
    except Exception as e:
        print(f"Error during testing: {e}")
    
    finally:
        # Clean up
        print("Terminating server...")
        server_process.terminate()
        server_process.wait()


if __name__ == "__main__":
    asyncio.run(test_mcp_server())
