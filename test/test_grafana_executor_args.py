#!/usr/bin/env python3
"""
测试 grafana_executor.py 的命令行参数功能
"""

import subprocess
import sys
import os

def test_help_option():
    """测试 --help 选项"""
    print("Testing --help option...")
    result = subprocess.run([sys.executable, "../grafana_executor.py", "--help"], 
                          capture_output=True, text=True, cwd=os.path.dirname(__file__))
    assert result.returncode == 0
    assert "Grafana brute-force login tool" in result.stdout
    assert "-t TEMPLATE, --template TEMPLATE" in result.stdout
    print("✅ --help option works correctly")

def test_missing_required_arg():
    """测试缺少必需参数的情况"""
    print("Testing missing required argument...")
    result = subprocess.run([sys.executable, "../grafana_executor.py"], 
                          capture_output=True, text=True, cwd=os.path.dirname(__file__))
    assert result.returncode == 2
    assert "the following arguments are required: -t/--template" in result.stderr
    print("✅ Missing required argument handled correctly")

def test_template_arg():
    """测试模板参数"""
    print("Testing template argument...")
    # 使用现有的模板文件
    test_template = "../templates/test-dvwa.yaml"
    
    # 测试短选项
    result = subprocess.run([sys.executable, "../grafana_executor.py", "-t", test_template], 
                          capture_output=True, text=True, timeout=15, cwd=os.path.dirname(__file__))
    # 由于目标可能不可访问或字典文件问题，但至少模板参数被正确解析了
    assert ("Username file not found" in result.stdout or 
            "Target is not accessible" in result.stdout or
            "Checking target connectivity" in result.stdout)
    print("✅ Short template argument (-t) works correctly")

def main():
    """运行所有测试"""
    print("Running grafana_executor.py argument tests...\n")
    
    try:
        test_help_option()
        test_missing_required_arg()
        test_template_arg()
        print("\n🎉 All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
