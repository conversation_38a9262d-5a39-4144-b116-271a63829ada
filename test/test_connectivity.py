#!/usr/bin/env python3
"""
测试连通性检查功能的简单脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from grafana_executor import check_target_connectivity

def test_connectivity():
    """测试连通性检查功能"""
    config_file = "templates/test-dvwa.yaml"
    
    print("Testing connectivity check function...")
    result = check_target_connectivity(config_file)
    
    if result:
        print("✅ Connectivity check passed - target is accessible")
    else:
        print("❌ Connectivity check failed - target is not accessible")
    
    return result

if __name__ == "__main__":
    test_connectivity()
