<!DOCTYPE html><html lang="en"><head><style data-emotion="css-global" data-s=""></style><style data-emotion="css-global" data-s=""></style><style data-emotion="css" data-s=""></style><meta charset="utf-8"><base href="/"><meta content="width=device-width,initial-scale=1" name="viewport"><meta content="#081C42" media="(prefers-color-scheme: light)" name="theme-color"><meta content="#081C42" media="(prefers-color-scheme: dark)" name="theme-color"><meta content="MinIO Console" name="description"><meta name="minio-license" content="agpl"><link href="./styles/root-styles.css" rel="stylesheet"><link href="./apple-icon-180x180.png" rel="apple-touch-icon" sizes="180x180"><link href="./favicon-32x32.png" rel="icon" sizes="32x32" type="image/png"><link href="./favicon-96x96.png" rel="icon" sizes="96x96" type="image/png"><link href="./favicon-16x16.png" rel="icon" sizes="16x16" type="image/png"><link href="./manifest.json" rel="manifest"><link color="#3a4e54" href="./safari-pinned-tab.svg" rel="mask-icon"><title>MinIO Console</title><script defer="defer" src="./static/js/main.5efdfa93.js"></script><link href="./static/css/main.57e739f5.css" rel="stylesheet"><style data-jss="" data-meta="makeStyles">
.jss35 .MuiOutlinedInput-root {
  padding-left: 0;
}
.jss35 .MuiOutlinedInput-root svg {
  color: #081C42;
  height: 14px;
  margin-left: 4px;
}
.jss35 .MuiOutlinedInput-root input {
  padding: 10px;
  font-size: 14px;
  padding-left: 0;
}
.jss35 .MuiOutlinedInput-root fieldset:hover {
  border-bottom: 2px solid #000000;
  border-radius: 0;
}
.jss35 .MuiOutlinedInput-root input::placeholder {
  font-size: 12px;
}
@media (max-width: 900px) {
  .jss35 .MuiOutlinedInput-root input {
    padding: 10px;
  }
}
</style><style data-jss="" data-meta="makeStyles">
.jss26 {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  position: absolute;
}
.jss27 {
  width: 100%;
}
.jss28 {
  width: 100%;
  height: 40px;
  margin: 30px 0px 8px;
  padding: 16px 30px;
  box-shadow: none;
}
.jss29 {
  margin-top: 30px;
  text-align: right;
}
.jss30 {
  height: 10px;
}
.jss31 {
  margin-right: .9rem;
}
.jss32 {
  margin-left: .9rem;
}
.jss33 {
  margin-bottom: .9rem;
}
.jss34 {
  margin-top: .9rem;
}
</style><style data-jss="" data-meta="makeStyles">
.jss1 {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  position: absolute;
}
.jss2 {
  width: 100%;
}
.jss3 {
  width: 100%;
  height: 40px;
  margin: 30px 0px 8px;
  padding: 16px 30px;
  box-shadow: none;
}
.jss4 {
  font-weight: 700;
  margin-bottom: 15px;
}
.jss5 {
  color: grey;
  width: 100%;
  font-size: 13px;
  font-weight: 700;
}
.jss6 {
  color: #073052;
  font-size: 15px;
  font-weight: 700;
}
.jss6.MuiMenuItem-divider:last-of-type {
  border-bottom: none;
}
.jss6.Mui-focusVisible {
  background-color: #f0f0f0;
}
.jss7 {
  height: 13px;
  margin-right: 25px;
}
.jss8 {
  margin-top: 15px;
}
.jss8:first-of-type {
  margin-top: 0;
}
.jss9 {
  margin-left: 4px;
  margin-right: 4px;
}
.jss10 {
  font: normal normal normal 14px/16px Inter;
  margin-top: 20px;
}
.jss11 {
  color: #B2DEF5;
  margin: auto;
  text-align: center;
}
.jss11 a {
  color: #B2DEF5;
  text-decoration: none;
}
.jss11 .min-icon {
  color: #B2DEF5;
  width: 10px;
}
.jss12 {
  margin-top: 8px;
}
.jss12 .min-icon {
  height: 12px;
  padding-top: 2px;
  margin-right: 2px;
}
.jss13 {
  height: 100%;
  margin: auto;
}
.jss14 {
  display: flex;
  justify-content: center;
}
.jss15 {
  margin: auto;
  max-width: 400px;
  flex-direction: column;
}
.jss15 .right-items {
  padding: 40px;
  background-color: white;
}
.jss15 .consoleTextBanner {
  flex: 1;
  color: #081C42;
  height: 100%;
  margin: auto;
  display: flex;
  font-size: calc(3vw + 3vh + 1.5vmin);
  font-weight: 300;
  line-height: 1.15;
  justify-content: flex-start;
}
.jss15 .consoleTextBanner .logoLine {
  display: flex;
  font-size: 18px;
  align-items: center;
}
.jss15 .consoleTextBanner .left-items {
  padding: 40px;
  background: transparent linear-gradient(180deg, #FBFAFA 0%, #E4E4E4 100%) 0% 0% no-repeat padding-box;
  margin-top: 100px;
}
.jss15 .consoleTextBanner .left-logo {
  margin-bottom: 10px;
}
.jss15 .consoleTextBanner .text-line1 {
  font:  100 44px 'Inter';
}
.jss15 .consoleTextBanner .text-line2 {
  font-size: 80px;
  font-weight: 100;
  text-transform: uppercase;
}
.jss15 .consoleTextBanner .text-line3 {
  font-size: 14px;
  font-weight: bold;
}
.jss15 .consoleTextBanner .logo-console {
  display: flex;
  align-items: center;
}
@media (max-width: 900px) {
  .jss15 .consoleTextBanner .logo-console {
    flex-flow: column;
    margin-top: 20px;
  }
  .jss15 .consoleTextBanner .logo-console svg {
    width: 50%;
  }
}
.jss15 .consoleTextBanner .left-logo .min-icon {
  color: #081C42;
  width: 108px;
}
@media (max-width: 900px) {
  .jss15 {
    display: flex;
    flex-flow: column;
  }
  .jss15 .consoleTextBanner {
    flex: 2;
    margin: 0;
  }
  .jss15 .consoleTextBanner .left-items {
    text-align: center;
    align-items: center;
  }
  .jss15 .consoleTextBanner .logoLine {
    justify-content: center;
  }
}
.jss16 {
  text-align: center;
}
.jss17 {
  width: 40px;
  height: 40px;
  text-align: center;
}
.jss18 {
  margin-top: 30px;
  text-align: right;
}
.jss19 {
  height: 10px;
}
.jss20 {
  align-self: flex-end;
}
.jss21 .min-icon {
  width: 100%;
}
.jss22 {
  margin-right: .9rem;
}
.jss23 {
  margin-left: .9rem;
}
.jss24 {
  margin-bottom: .9rem;
}
.jss25 {
  margin-top: .9rem;
}
</style><style data-jss="" data-meta="makeStyles">
.rowLine {
  color: #393939;
}
.detailsListPanel {
  background-color: #fff;
}
</style><style data-styled="active" data-styled-version="5.3.6"></style></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"><div class="sc-eDvSVe eczWhG"><div class="sc-hLBbgP jhPDBr mainContainer" wrap="nowrap"><div class="sc-hLBbgP gQMFXb decorationPanel"><div class="sc-hLBbgP ipKqCS"><div class="sc-hLBbgP bVBmg promoContainer"><div class="sc-hLBbgP bVBmg promoHeader">Multi-Cloud Object&nbsp;Store</div><div class="sc-hLBbgP bVBmg promoInfo">MinIO's high-performance, Kubernetes-native object store is licensed under GNU AGPL v3 and is available on every cloud - public, private and edge. For more information on the terms of the license or to learn more about commercial licensing options visit the <a href="https://min.io/pricing">pricing page</a>.</div></div></div><div class="sc-hLBbgP bVBmg videoContainer"><img src="./static/media/loginAnimationPoster.9aa924bfe619e71d5d29.png" class="videoBG"></div></div><div class="sc-hLBbgP bWXzON formPanel"><div class="sc-hLBbgP ipKqCS"><div class="sc-hLBbgP bWXzON logoContainer"><svg viewBox="0 0 184.45 54.229" class="sc-gswNZR kpIlFD"><g transform="translate(-31.65 -18.133)"><g transform="translate(-995 -63.754)"><g transform="translate(1025.5 81.887)"><g transform="translate(0 0)"><g transform="translate(0 0)"><path d="M10.338-17.825A8.815,8.815,0,0,0,1.15-8.75,8.815,8.815,0,0,0,10.338.325a8.825,8.825,0,0,0,9.2-9.075A8.825,8.825,0,0,0,10.338-17.825Zm0,3.35a5.4,5.4,0,0,1,5.55,5.725,5.4,5.4,0,0,1-5.55,5.725A5.41,5.41,0,0,1,4.788-8.75,5.41,5.41,0,0,1,10.338-14.475ZM22.05-17.5V0h7.575c4.2,0,6.588-1.65,6.588-5.013A4.2,4.2,0,0,0,33.3-8.938a3.9,3.9,0,0,0,2.537-3.713c0-3.337-2.562-4.85-6.638-4.85Zm7.4,10.225c1.925,0,3.138.45,3.138,2.088,0,1.675-1.212,2.125-3.138,2.125l-3.913-.013v-4.2Zm-.35-7.15c1.725,0,3.1.375,3.1,2.025,0,1.7-1.35,2.063-3.087,2.063H25.538v-4.088ZM48.788-17.5H45.3V-6.7c0,2.525-1.1,3.675-2.95,3.675a4.214,4.214,0,0,1-3.4-1.625L36.925-2.113A6.9,6.9,0,0,0,42.513.313c3.65,0,6.275-2.3,6.275-6.688ZM65.113-3.2H55.525V-7.225h9.05v-3.2h-9.05V-14.3h9.487v-3.2H52.037V0H65.113ZM76.3-17.825A8.794,8.794,0,0,0,67.113-8.75,8.794,8.794,0,0,0,76.3.325a8.713,8.713,0,0,0,7.387-3.7l-2.85-2.05a5.355,5.355,0,0,1-4.562,2.4A5.4,5.4,0,0,1,70.75-8.75a5.411,5.411,0,0,1,5.525-5.725A5.237,5.237,0,0,1,80.8-12.063l3-1.838A8.5,8.5,0,0,0,76.3-17.825Zm22.9.325H84.863v3.262h5.425V0h3.487V-14.238H99.2Zm19.787,1.738a10.5,10.5,0,0,0-6.25-1.925c-3.6,0-6.475,1.812-6.475,5.037,0,2.688,1.938,4.125,5.138,4.488l1.987.225c2.913.325,4.438,1.25,4.438,3.15,0,2.363-2.337,3.525-5.3,3.525a10.115,10.115,0,0,1-5.925-1.95L105.762-2A11.524,11.524,0,0,0,112.537.188c3.775,0,6.875-1.7,6.875-5.1,0-2.913-2.262-4.138-5.375-4.488l-1.912-.212c-2.988-.338-4.275-1.4-4.275-3.138,0-2.187,2.063-3.488,4.875-3.488a9.323,9.323,0,0,1,5.475,1.713ZM135.025-17.5H120.888v1.45h6.3V0h1.525V-16.05h6.313Zm9.875-.2a8.672,8.672,0,0,0-8.963,8.95A8.672,8.672,0,0,0,144.9.2a8.672,8.672,0,0,0,8.962-8.95A8.672,8.672,0,0,0,144.9-17.7Zm0,1.475a7.174,7.174,0,0,1,7.363,7.475A7.174,7.174,0,0,1,144.9-1.275a7.177,7.177,0,0,1-7.375-7.475A7.177,7.177,0,0,1,144.9-16.225ZM157.413-17.5V0h1.525V-7.763h2.675L168.138,0h1.9l-6.625-7.763h.688c3.725,0,6.025-1.862,6.025-4.875,0-3.1-2.175-4.863-6.037-4.863Zm6.663,1.438c2.875,0,4.475,1.188,4.475,3.425s-1.575,3.488-4.475,3.488h-5.138v-6.913ZM185.6-1.438H175.075V-8.1h10.138V-9.525H175.075v-6.538h10.438V-17.5H173.55V0H185.6Z" transform="translate(0 32.612)" class="minioApplicationName"></path><g transform="translate(2.003)"><g transform="translate(0 0.129)"><rect width="2.49" height="7.352" transform="translate(14.42)" class="minioSection"></rect><path d="M237.8,365.332l-5.053,3.086a.226.226,0,0,1-.235,0l-5.053-3.086a.694.694,0,0,0-.362-.1H227.1a.693.693,0,0,0-.693.693v6.65h2.489v-3.165a.249.249,0,0,1,.379-.212l2.832,1.733a.886.886,0,0,0,.912.009L236,369.184a.249.249,0,0,1,.374.215v3.174h2.488v-6.65a.693.693,0,0,0-.692-.693h-.006A.694.694,0,0,0,237.8,365.332Z" transform="translate(-226.403 -365.23)" class="minioSection"></path><path d="M257.822,365.23H255.3v3.346a.249.249,0,0,1-.366.22l-6.543-3.485a.7.7,0,0,0-.326-.081h0a.693.693,0,0,0-.693.693v6.651h2.5v-3.343a.249.249,0,0,1,.365-.22L256.8,372.5a.692.692,0,0,0,.325.081h0a.693.693,0,0,0,.693-.693Z" transform="translate(-228.498 -365.23)" class="minioSection"></path></g><path d="M261.159,372.582V365.23H262.3v7.352Z" transform="translate(-229.877 -365.101)" class="minioSection"></path><path d="M269.337,372.7c-3.082,0-5.268-1.462-5.268-3.805s2.2-3.806,5.268-3.806,5.281,1.462,5.281,3.806S272.458,372.7,269.337,372.7Zm0-6.637c-2.292,0-4.056,1-4.056,2.832s1.765,2.831,4.056,2.831,4.07-.988,4.07-2.831S271.628,366.062,269.337,366.062Z" transform="translate(-230.168 -365.087)" class="minioSection"></path></g></g></g></g><g transform="translate(1168.671 120.754)"><g transform="translate(-65 0)"><path d="M106.959,1769.479l-3.274,14.286h31.641a2.814,2.814,0,0,1-2.121-1.012,2.15,2.15,0,0,1-.209-.356c-.038-.092-.073-.185-.109-.28a2.832,2.832,0,0,1-.115-.985,7.182,7.182,0,0,1,1.312-3.389,18.271,18.271,0,0,1,3.616-3.945c.343-.284.7-.566,1.068-.839.458-.337.92-.648,1.383-.938a17.592,17.592,0,0,1,4.907-2.2,18.957,18.957,0,0,0-4.651,2.351l-.171.118a20.8,20.8,0,0,0-2.389,1.924c-2.254,2.119-3.445,4.315-2.9,5.6a1.6,1.6,0,0,0,.138.253c.582.856,2.024,1,3.851.544.124-.031.249-.067.377-.1a14.878,14.878,0,0,0,1.842-.677c.153-.068.309-.137.465-.212l.047-.023c2.015-1,3.563-2.153,3.9-2.845a.43.43,0,0,0,.041-.379c-.239-.485-1.912-.157-3.939.72-.163.07-.328.143-.494.221.136-.125.277-.252.421-.377.23-.2.468-.391.721-.582a14.277,14.277,0,0,1,1.191-.812c1.847-1.394,2.781-2.712,2.586-3.2a.343.343,0,0,0-.235-.194,3.4,3.4,0,0,0-1.942.374,14.514,14.514,0,0,0-2.333,1.25l-.112.073-.021.012-.394.262.226-.415a7.126,7.126,0,0,1,1.565-1.853,11.116,11.116,0,0,1,1.686-1.206c.233-.136.465-.262.7-.376s.476-.22.709-.312a8.2,8.2,0,0,1,1.98-.649c-.051,0-1.677.175-1.677.175H106.959Zm25.5.021a19.123,19.123,0,0,0,.8,5.76q.165.612.362,1.242.123.388.253.765c-.051.075-.1.149-.15.224a7.909,7.909,0,0,0-1.339,3.277,20.169,20.169,0,0,1-.712-3.562q-.059-.546-.091-1.08a15.688,15.688,0,0,1,.877-6.625Zm-15.424,1.833h3.533a1.217,1.217,0,0,1,.691.168.394.394,0,0,1,.185.435l-.415,1.874h-1.227l.4-1.824h-3.071L116.03,1777l-.4,1.815H118.7l0-.011.615-2.778h-1.442l.138-.626h2.668l-.765,3.466a.488.488,0,0,1-.053.138.765.765,0,0,1-.327.294,1.621,1.621,0,0,1-.765.168h-3.477a1.214,1.214,0,0,1-.691-.168.388.388,0,0,1-.185-.432l1.533-6.928a.664.664,0,0,1,.377-.435c.008,0,.016,0,.024-.009a1.6,1.6,0,0,1,.688-.159Zm5.454,0h4.38a1.215,1.215,0,0,1,.688.168.392.392,0,0,1,.188.435l-.818,3.695a.663.663,0,0,1-.38.433,1.612,1.612,0,0,1-.762.171h-3.183l-.615,2.774-.1.456h-1.2l.091-.412Zm6.051,0h1.2l-1.359,6.14-.3,1.341h2.871c.03.22.065.437.1.65h-4.319l.341-1.542Zm-5,.653-.8,3.6h2.992l.794-3.6Zm-6.38,8.485h.035a.85.85,0,0,1,.359.07.428.428,0,0,1,.221.218.532.532,0,0,1,.029.315l-.009.044h-.344l0-.041a.271.271,0,0,0-.032-.188l-.015-.018a.2.2,0,0,0-.029-.024.426.426,0,0,0-.221-.047.511.511,0,0,0-.291.068.258.258,0,0,0-.118.153.113.113,0,0,0,.024.109l0,0a.81.81,0,0,0,.291.1,2,2,0,0,1,.38.12.448.448,0,0,1,.218.209.458.458,0,0,1,.024.291.665.665,0,0,1-.156.291.789.789,0,0,1-.3.212,1,1,0,0,1-.382.076.955.955,0,0,1-.412-.076.473.473,0,0,1-.238-.244.6.6,0,0,1-.029-.356l.009-.041h.338l0,.041a.373.373,0,0,0,.021.189.23.23,0,0,0,.118.112.543.543,0,0,0,.235.047.649.649,0,0,0,.224-.038.4.4,0,0,0,.156-.094.261.261,0,0,0,.068-.126.138.138,0,0,0-.009-.1.214.214,0,0,0-.109-.08l-.288-.085a1.274,1.274,0,0,1-.332-.118.411.411,0,0,1-.18-.194.418.418,0,0,1-.015-.256.622.622,0,0,1,.144-.28.72.72,0,0,1,.288-.2A1.01,1.01,0,0,1,117.169,1780.47Zm3.089.006c.019,0,.036,0,.056,0l.212.023.071.006-.1.262-.021.041-.162-.015a.186.186,0,0,0-.106.023l-.006.006-.012.012a.279.279,0,0,0-.044.112l-.012.047h.253l-.065.292h-.247l-.25,1.121h-.341s.222-1,.25-1.121h-.2l.065-.292h.194c.009-.04.024-.091.024-.091a.717.717,0,0,1,.071-.209.441.441,0,0,1,.162-.159.491.491,0,0,1,.209-.059Zm.815.015-.112.5h.221l-.065.292H120.9c-.018.081-.159.709-.159.709s-.012.076-.012.1c0,0,0,0,0,0s0,0,0,0h0l.035,0,.162-.012-.018.262,0,.047-.232.026a.375.375,0,0,1-.209-.047.209.209,0,0,1-.094-.135.221.221,0,0,1-.006-.047,1.206,1.206,0,0,1,.035-.239s.124-.554.15-.671h-.162l.065-.292h.162c.015-.068.068-.3.068-.3l.274-.144.112-.059Zm-10.841.011h1.324l-.074.329h-.968l-.1.436h.838l-.074.329h-.838c-.018.082-.179.809-.179.809h-.356Zm1.774.465a.331.331,0,0,1,.041,0,.4.4,0,0,1,.238.079l.047.032-.182.3-.05-.035a.214.214,0,0,0-.118-.036.185.185,0,0,0-.1.036.258.258,0,0,0-.088.1.93.93,0,0,0-.088.241l-.159.724H111.2l.315-1.413h.318s-.011.043-.015.059c.015-.012.031-.027.044-.035A.358.358,0,0,1,112.006,1780.968Zm1.012,0c.021,0,.041,0,.062,0a.5.5,0,0,1,.432.2.545.545,0,0,1,.091.317,1.064,1.064,0,0,1-.026.227l-.026.1h-.959c0,.02,0,.041,0,.059a.28.28,0,0,0,.047.173.216.216,0,0,0,.053.053.261.261,0,0,0,.144.038.339.339,0,0,0,.188-.056.5.5,0,0,0,.153-.167h.365l-.032.07a.806.806,0,0,1-.288.329.779.779,0,0,1-.427.121.531.531,0,0,1-.459-.2.644.644,0,0,1-.065-.536.975.975,0,0,1,.3-.541.76.76,0,0,1,.45-.191Zm1.533,0c.021,0,.041,0,.062,0a.5.5,0,0,1,.432.2.545.545,0,0,1,.091.317,1.04,1.04,0,0,1-.026.224l-.026.106h-.959l0,.038s0,.012,0,.018v0c0,.013,0,.028,0,.041a.254.254,0,0,0,.044.132.227.227,0,0,0,.015.021.239.239,0,0,0,.182.071.336.336,0,0,0,.188-.056.5.5,0,0,0,.153-.167h.368l-.035.07a.806.806,0,0,1-.288.329.779.779,0,0,1-.427.121.49.49,0,0,1-.55-.52c0-.02,0-.041,0-.062a1.067,1.067,0,0,1,.024-.153.975.975,0,0,1,.3-.541A.768.768,0,0,1,114.551,1780.968Zm4.175,0c.021,0,.04,0,.062,0a.523.523,0,0,1,.444.2.627.627,0,0,1,.071.529,1.086,1.086,0,0,1-.171.415.811.811,0,0,1-.644.326.516.516,0,0,1-.444-.2.528.528,0,0,1-.094-.321,1.011,1.011,0,0,1,.026-.227.925.925,0,0,1,.341-.568.794.794,0,0,1,.409-.153Zm5.169,0c.025,0,.048,0,.074,0a.748.748,0,0,1,.282.041.31.31,0,0,1,.159.124.337.337,0,0,1,.044.179l-.035.215-.065.291a3.187,3.187,0,0,0-.071.377.377.377,0,0,0,.015.135l.024.077h-.347l-.015-.045a.417.417,0,0,1-.006-.07,1.03,1.03,0,0,1-.191.1.83.83,0,0,1-.271.047.446.446,0,0,1-.35-.123.313.313,0,0,1-.079-.218.474.474,0,0,1,.012-.1.492.492,0,0,1,.091-.2.55.55,0,0,1,.159-.141.71.71,0,0,1,.191-.077l.209-.035a2.331,2.331,0,0,0,.368-.068.185.185,0,0,1,.006-.021.188.188,0,0,0,0-.129l-.006-.006-.012-.012a.29.29,0,0,0-.177-.041.391.391,0,0,0-.206.044.382.382,0,0,0-.127.159h-.356l.032-.071a.75.75,0,0,1,.156-.241.648.648,0,0,1,.247-.144A.974.974,0,0,1,123.895,1780.968Zm1.492,0a.331.331,0,0,1,.041,0,.4.4,0,0,1,.241.079l.044.032-.182.3-.05-.035a.207.207,0,0,0-.115-.036.2.2,0,0,0-.106.036.259.259,0,0,0-.085.1.965.965,0,0,0-.088.241l-.162.724h-.341l.315-1.413h.318s-.008.043-.012.059a.536.536,0,0,1,.044-.035A.342.342,0,0,1,125.386,1780.968Zm1.009,0c.02,0,.041,0,.062,0a.5.5,0,0,1,.432.2.538.538,0,0,1,.091.317,1.077,1.077,0,0,1-.029.227l-.024.1h-.959c0,.02-.006.041-.006.059a.286.286,0,0,0,.047.173.251.251,0,0,0,.018.021l.012.012a.246.246,0,0,0,.171.059.339.339,0,0,0,.188-.056.508.508,0,0,0,.153-.167h.368l-.035.07a.813.813,0,0,1-.288.329.779.779,0,0,1-.427.121.525.525,0,0,1-.456-.2.647.647,0,0,1-.068-.536.972.972,0,0,1,.3-.541A.77.77,0,0,1,126.4,1780.968Zm-5.151.026h.35s.043.838.044.85c.014-.03.025-.055.026-.059l.385-.792h.321s.029.828.029.833l.438-.833h.347l-.765,1.413h-.315s-.03-.766-.032-.809l-.394.809h-.324Zm-8.22.268a.374.374,0,0,0-.224.088.433.433,0,0,0-.121.167h.58c0-.01,0-.023,0-.032a.244.244,0,0,0-.026-.123.207.207,0,0,0-.194-.1Zm1.533,0a.374.374,0,0,0-.224.088.443.443,0,0,0-.121.167h.58c0-.01,0-.023,0-.032a.234.234,0,0,0-.026-.123.244.244,0,0,0-.029-.038.219.219,0,0,0-.165-.062Zm11.856,0a.381.381,0,0,0-.232.088.454.454,0,0,0-.121.167h.577c0-.01,0-.023,0-.032a.234.234,0,0,0-.027-.123.21.21,0,0,0-.194-.1Zm-7.708.006a.39.39,0,0,0-.218.106.637.637,0,0,0-.174.341.779.779,0,0,0-.021.168.289.289,0,0,0,.038.159.316.316,0,0,0,.024.03.229.229,0,0,0,.174.068.372.372,0,0,0,.259-.109.654.654,0,0,0,.174-.347.419.419,0,0,0-.018-.317.213.213,0,0,0-.194-.1C118.734,1781.267,118.72,1781.266,118.705,1781.267Zm5.316.515a2.16,2.16,0,0,1-.288.056.968.968,0,0,0-.188.042.208.208,0,0,0-.079.056.173.173,0,0,0-.041.077.2.2,0,0,0,0,.032s0,0,0,0,0,.007,0,.009a.113.113,0,0,0,0,.015l0,.006a.087.087,0,0,0,0,.009l.006.009.009.012a.185.185,0,0,0,.138.038.465.465,0,0,0,.212-.047.409.409,0,0,0,.156-.135A.545.545,0,0,0,124.021,1781.782Zm-17.969-2.359,7.9-8.152h1.289l-1.906,8.152H112.27l.541-2.347H109.5l-2.249,2.347h-1.2m4.254-3.186h2.707l.5-2.047q.3-1.217.582-2.029-.559.7-1.479,1.662l-2.309,2.413" transform="translate(-103.684 -1768.875)" class="minioApplicationName"></path><path d="M627.829,1776.9a3.183,3.183,0,0,1-2.4-1.149,2.464,2.464,0,0,1-.241-.411c-.045-.107-.084-.207-.123-.307l.439-.17c.038.1.075.193.114.287a2,2,0,0,0,.19.323,2.685,2.685,0,0,0,2.04.958h1.032a9.027,9.027,0,0,0,1-.141,12.945,12.945,0,0,0,1.935-.55c.524-.191,1.054-.415,1.575-.666a22.265,22.265,0,0,0,3.559-2.154c.377-.278.756-.574,1.124-.881q.494-.411.947-.834a9.057,9.057,0,0,0,1.807-2.317c.348-.7.407-1.259.167-1.576a.989.989,0,0,0-.749-.326l-.622-.048.5-.375c1.786-1.34,2.8-2.927,2.457-3.858a1,1,0,0,0-.638-.59,2.032,2.032,0,0,0-.516-.106h-.549a8.415,8.415,0,0,0-2.824.8l-.207-.423a8.932,8.932,0,0,1,3.014-.845h.585a2.509,2.509,0,0,1,.656.133,1.455,1.455,0,0,1,.921.871c.387,1.063-.5,2.665-2.216,4.081a1.2,1.2,0,0,1,.564.4,1.959,1.959,0,0,1-.121,2.07,9.408,9.408,0,0,1-1.9,2.449q-.466.435-.97.854c-.376.313-.761.615-1.146.9a22.77,22.77,0,0,1-3.635,2.2c-.535.257-1.079.487-1.617.683a13.4,13.4,0,0,1-2.006.569,9.406,9.406,0,0,1-1.07.148Z" transform="translate(-596.283 -1761.542)" class="minioApplicationName"></path></g></g></g><path d="M.969,0V-8h.969V-.859H5.656V0ZM9.225-8V0H8.256V-8ZM18.7-5.5h-.969a2.034,2.034,0,0,0-.3-.734,2.072,2.072,0,0,0-.516-.533,2.24,2.24,0,0,0-.67-.326,2.668,2.668,0,0,0-.766-.109,2.431,2.431,0,0,0-1.314.367,2.536,2.536,0,0,0-.934,1.082A4.007,4.007,0,0,0,12.887-4a4.007,4.007,0,0,0,.346,1.754,2.536,2.536,0,0,0,.934,1.082A2.431,2.431,0,0,0,15.481-.8a2.668,2.668,0,0,0,.766-.109,2.24,2.24,0,0,0,.67-.326,2.06,2.06,0,0,0,.516-.535,2.053,2.053,0,0,0,.3-.732H18.7a3.227,3.227,0,0,1-.4,1.1,2.973,2.973,0,0,1-.719.822,3.129,3.129,0,0,1-.963.514,3.614,3.614,0,0,1-1.139.176,3.353,3.353,0,0,1-1.82-.5,3.431,3.431,0,0,1-1.254-1.422A4.874,4.874,0,0,1,11.95-4a4.874,4.874,0,0,1,.457-2.187,3.431,3.431,0,0,1,1.254-1.422,3.353,3.353,0,0,1,1.82-.5,3.614,3.614,0,0,1,1.139.176,3.129,3.129,0,0,1,.963.514,2.984,2.984,0,0,1,.719.82A3.208,3.208,0,0,1,18.7-5.5ZM21.362,0V-8h4.828v.859H22.331v2.7h3.609v.859H22.331V-.859h3.922V0ZM35.384-8V0h-.937L30.087-6.281h-.078V0h-.969V-8h.938l4.375,6.3h.078V-8ZM42.8-6a1.226,1.226,0,0,0-.57-.922A2.188,2.188,0,0,0,41-7.25a2.317,2.317,0,0,0-.928.172,1.468,1.468,0,0,0-.617.473,1.126,1.126,0,0,0-.221.684.957.957,0,0,0,.154.549,1.3,1.3,0,0,0,.4.379,2.686,2.686,0,0,0,.508.246q.266.1.488.154l.813.219a7.221,7.221,0,0,1,.7.227,3.309,3.309,0,0,1,.738.393,2.04,2.04,0,0,1,.584.635,1.824,1.824,0,0,1,.23.949A2.115,2.115,0,0,1,43.506-1a2.329,2.329,0,0,1-.984.832,3.618,3.618,0,0,1-1.568.309,3.653,3.653,0,0,1-1.486-.277,2.355,2.355,0,0,1-.984-.773,2.2,2.2,0,0,1-.4-1.152h1a1.236,1.236,0,0,0,.307.748,1.608,1.608,0,0,0,.68.438,2.7,2.7,0,0,0,.889.143,2.6,2.6,0,0,0,1-.182,1.687,1.687,0,0,0,.7-.508,1.2,1.2,0,0,0,.258-.764.938.938,0,0,0-.223-.648,1.634,1.634,0,0,0-.586-.406,6.157,6.157,0,0,0-.785-.273L40.328-3.8a3.666,3.666,0,0,1-1.484-.77A1.69,1.69,0,0,1,38.3-5.875a1.942,1.942,0,0,1,.365-1.174,2.417,2.417,0,0,1,.984-.781,3.331,3.331,0,0,1,1.385-.279,3.269,3.269,0,0,1,1.375.275,2.409,2.409,0,0,1,.955.752A1.875,1.875,0,0,1,43.734-6Zm3.741,6V-8h4.828v.859H47.506v2.7h3.609v.859H47.506V-.859h3.922V0Z" transform="translate(164 68)" class="minioApplicationName"></path></g></svg></div><div class="sc-hLBbgP bWXzON formContainer"><div class="sc-hLBbgP naHPD form"><form class="jss27" novalidate=""><div class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-isbt42"><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss33 css-15j76c0"><div class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root jss35 css-feqhe6"><div class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-adornedStart css-7lqkqc"><div class="MuiInputAdornment-root MuiInputAdornment-positionStart MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1a6giau"><span class="notranslate">​</span><svg xmlns="http://www.w3.org/2000/svg" class="min-icon" fill="currentcolor" viewBox="0 0 9.008 12"><defs><clipPath id="clip-path"><rect id="Rectangle_991" data-name="Rectangle 991" width="9.008" height="12" fill="#071d43"></rect></clipPath></defs><g id="Group_2365" data-name="Group 2365"><path id="Path_7088" data-name="Path 7088" d="M26.843,6.743a3.4,3.4,0,0,0,3.411-3.372,3.411,3.411,0,0,0-6.822,0,3.4,3.4,0,0,0,3.411,3.372" transform="translate(-22.334)" fill="#071d43"></path><path id="Path_7089" data-name="Path 7089" d="M8.639,157.057a5.164,5.164,0,0,0-1.957-1.538,5.438,5.438,0,0,0-1.083-.362,5.2,5.2,0,0,0-1.117-.123c-.075,0-.151,0-.225.005H4.231a4.928,4.928,0,0,0-.549.059,5.236,5.236,0,0,0-3.276,1.92c-.029.039-.059.078-.086.116h0a1.723,1.723,0,0,0-.134,1.784,1.583,1.583,0,0,0,.255.356,1.559,1.559,0,0,0,.337.267,1.613,1.613,0,0,0,.4.167,1.742,1.742,0,0,0,.449.058H7.389a1.747,1.747,0,0,0,.452-.058,1.593,1.593,0,0,0,.4-.169,1.524,1.524,0,0,0,.335-.271,1.548,1.548,0,0,0,.251-.361,1.761,1.761,0,0,0-.191-1.85" transform="translate(0.001 -147.766)" fill="#071d43"></path></g></svg></div><input aria-invalid="false" autocomplete="username" id="accessKey" name="accessKey" placeholder="Username" type="text" class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g" value=""><fieldset aria-hidden="true" class="MuiOutlinedInput-notchedOutline css-igs3ac"><legend class="css-ihdtdm"><span class="notranslate">​</span></legend></fieldset></div></div></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-15j76c0"><div class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root jss35 css-feqhe6"><div class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-adornedStart css-7lqkqc"><div class="MuiInputAdornment-root MuiInputAdornment-positionStart MuiInputAdornment-outlined MuiInputAdornment-sizeMedium css-1a6giau"><span class="notranslate">​</span><svg xmlns="http://www.w3.org/2000/svg" class="min-icon" fill="currentcolor" viewBox="0 0 12 12"><path id="Path_7819" data-name="Path 7819" d="M9.884,3.523H8.537V2.27A2.417,2.417,0,0,0,6,0,2.417,2.417,0,0,0,3.463,2.27V3.523H2.116A2.019,2.019,0,0,0,0,5.423V9.413a2.012,2.012,0,0,0,2.062,1.9L6,12l3.938-.688A2.012,2.012,0,0,0,12,9.413V5.423a2.019,2.019,0,0,0-2.116-1.9M6.5,7.658v.724a.474.474,0,0,1-.472.474H5.971A.474.474,0,0,1,5.5,8.381V7.658a.9.9,0,0,1-.394-.744h0a.894.894,0,1,1,1.4.744m.985-4.135H4.514V2.27A1.416,1.416,0,0,1,6,.94,1.416,1.416,0,0,1,7.486,2.27Z" fill="#071d43"></path></svg></div><input aria-invalid="false" autocomplete="current-password" id="secretKey" name="secretKey" placeholder="Password" type="password" class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedStart css-1ixds2g" value=""><fieldset aria-hidden="true" class="MuiOutlinedInput-notchedOutline css-igs3ac"><legend class="css-ihdtdm"><span class="notranslate">​</span></legend></fieldset></div></div></div></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss29 css-1wxaqej"><button disabled="" label="Login" type="submit" color="primary" id="do-login" class="sc-bcXHqe cudOsJ jss28"><span class="button-label">Login</span></button></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss30 css-1wxaqej"></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 jss30 css-1wxaqej"><div class="MuiBox-root css-0" style="text-align: center; margin-top: 20px;"><span style="color: rgb(39, 129, 176); font: 14px Inter; text-decoration: underline; cursor: pointer;">Use STS</span><span style="color: rgb(39, 129, 176); font: bold 12px / 15px Inter; text-decoration: none; padding-left: 4px;">➔</span></div></div></form></div><div class="sc-hLBbgP naHPD footer"><a href="https://min.io/docs/minio/linux/index.html?ref=con" target="_blank" rel="noopener">Documentation</a><span class="jss9">|</span><a href="https://github.com/minio/minio" target="_blank" rel="noopener">Github</a><span class="jss9">|</span><a href="https://subnet.min.io/?ref=con" target="_blank" rel="noopener">Support</a><span class="jss9">|</span><a href="https://min.io/download/?ref=con" target="_blank" rel="noopener">Download</a></div></div></div></div></div></div></div></body></html>