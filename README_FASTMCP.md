# FastMCP Playwright DOM Extractor

这是一个使用 FastMCP 框架实现的 MCP (Model Context Protocol) 服务器，通过 Playwright 加载指定网页并提取 DOM 内容。

## 功能特性

- 🌐 使用 Playwright 加载网页
- 📄 提取完整的 DOM 内容
- 🔍 智能识别登录表单元素
- 🎯 支持 CSS 选择器定位特定元素
- 🖥️ 支持无头模式和有头模式
- 📊 提供详细的元素分析报告

## 安装依赖

项目使用 `uv` 进行依赖管理：

```bash
# 安装 FastMCP
uv add fastmcp

# 安装 Playwright 浏览器
uv run python -m playwright install
```

## 可用工具

### 1. load_page
加载指定的网页。

**参数：**
- `url` (string): 要加载的 URL
- `wait_for` (string, 可选): 等待条件 (load, domcontentloaded, networkidle)
- `timeout` (int, 可选): 超时时间（毫秒），默认 30000
- `headless` (bool, 可选): 是否使用无头模式，默认 true

### 2. get_dom
获取当前页面的 DOM 内容。

**参数：**
- `selector` (string, 可选): CSS 选择器，用于提取特定元素
- `include_styles` (bool, 可选): 是否包含计算样式，默认 false
- `pretty_print` (bool, 可选): 是否格式化输出，默认 true

### 3. find_login_elements
查找页面中的登录表单元素。

**参数：**
- `include_html` (bool, 可选): 是否包含元素的 HTML 内容，默认 true

### 4. close_browser
关闭浏览器实例。

## 使用方法

### 方法 1: 直接运行服务器

```bash
# 启动 FastMCP 服务器
uv run python playwright_mcp_server.py
```

### 方法 2: 使用演示脚本

```bash
# 运行演示脚本，加载 http://localhost:9001/login 并提取 DOM
uv run python demo_load_target.py
```

## 演示脚本功能

`demo_load_target.py` 脚本演示了完整的使用流程：

1. 🚀 启动 FastMCP 服务器
2. 📋 列出可用工具
3. 🌐 加载目标页面 (http://localhost:9001/login)
4. 📄 提取完整 DOM 内容并保存到 `extracted_dom.html`
5. 🔍 查找登录表单元素并保存到 `login_elements.json`
6. 🔒 关闭浏览器

## 输出文件

运行演示脚本后会生成以下文件：

- `extracted_dom.html`: 完整的页面 DOM 内容
- `login_elements.json`: 登录表单元素的详细信息

## 示例输出

```json
{
  "status": "success",
  "url": "http://localhost:9001/login",
  "found_elements": {
    "forms": {
      "count": 1,
      "elements": [...]
    },
    "username_inputs": {
      "count": 1,
      "elements": [...]
    },
    "password_inputs": {
      "count": 1,
      "elements": [...]
    },
    "submit_buttons": {
      "count": 1,
      "elements": [...]
    }
  },
  "summary": {
    "total_forms": 1,
    "total_username_inputs": 1,
    "total_password_inputs": 1,
    "total_submit_buttons": 1,
    "total_inputs": 2,
    "total_buttons": 1
  }
}
```

## 项目结构

```
.
├── playwright_mcp_server.py    # FastMCP 服务器主文件
├── demo_load_target.py         # 演示脚本
├── run_fastmcp_server.py       # 服务器启动脚本
├── test_fastmcp_client.py      # 测试客户端
├── pyproject.toml              # 项目配置
└── README_FASTMCP.md           # 本文档
```

## 技术特点

- **FastMCP 框架**: 使用现代化的 FastMCP 框架，简化 MCP 服务器开发
- **异步处理**: 全异步设计，支持高并发
- **智能等待**: 自动等待页面加载和网络空闲状态
- **元素识别**: 智能识别各种类型的登录表单元素
- **错误处理**: 完善的错误处理和状态报告
- **灵活配置**: 支持多种配置选项和参数

## 注意事项

1. 确保目标服务器 (http://localhost:9001) 正在运行
2. 首次运行需要下载 Playwright 浏览器
3. 无头模式下不会显示浏览器窗口
4. 生成的文件会覆盖同名的现有文件
