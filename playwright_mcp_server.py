#!/usr/bin/env python3
"""
FastMCP Server for Playwright DOM extraction.
Provides tools to load web pages and extract DOM content using Playwright.
"""

import async<PERSON>
import j<PERSON>
from typing import Any, Dict, Optional
from playwright.async_api import async_playwright, <PERSON>rowser, <PERSON>
from fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("playwright-dom-extractor")

# Global browser state
playwright_instance = None
browser: Optional[Browser] = None
page: Optional[Page] = None


async def ensure_browser(headless: bool = True):
    """Ensure browser is initialized."""
    global playwright_instance, browser, page
    
    if not playwright_instance:
        playwright_instance = await async_playwright().start()
    
    if not browser:
        browser = await playwright_instance.chromium.launch(headless=headless)
    
    if not page:
        page = await browser.new_page()


@mcp.tool()
async def load_page(
    url: str,
    wait_for: str = "load",
    timeout: int = 30000,
    headless: bool = True
) -> str:
    """
    Load a web page using <PERSON>wright and extract its DOM.
    
    Args:
        url: The URL to load
        wait_for: What to wait for after loading (load, domcontentloaded, networkidle)
        timeout: Timeout in milliseconds
        headless: Run browser in headless mode
    
    Returns:
        JSON string with page load result
    """
    global page
    
    try:
        await ensure_browser(headless)

        # Navigate to the page
        await page.goto(url, wait_until=wait_for, timeout=timeout)

        # Wait a bit more for JavaScript to load dynamic content
        if wait_for != "networkidle":
            try:
                await page.wait_for_load_state("networkidle", timeout=5000)
            except:
                pass  # Continue even if networkidle timeout

        # Wait for common login form elements to appear
        try:
            await page.wait_for_selector("input, form, button", timeout=3000)
        except:
            pass  # Continue even if no form elements found

        # Get basic page info
        title = await page.title()
        current_url = page.url

        result = {
            "status": "success",
            "url": current_url,
            "title": title,
            "message": f"Successfully loaded page: {title}"
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "url": url,
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def get_dom(
    selector: Optional[str] = None,
    include_styles: bool = False,
    pretty_print: bool = True
) -> str:
    """
    Get the current page's DOM content.
    
    Args:
        selector: CSS selector to extract specific elements (optional)
        include_styles: Include computed styles
        pretty_print: Format the HTML output
    
    Returns:
        JSON string with DOM content
    """
    global page
    
    if not page:
        return json.dumps({"error": "No page loaded. Use load_page first."}, indent=2)
    
    try:
        if selector:
            # Get specific elements
            elements = await page.locator(selector).all()
            if not elements:
                return json.dumps({"error": f"No elements found for selector: {selector}"}, indent=2)
            
            html_content = ""
            for element in elements:
                html_content += await element.inner_html()
                html_content += "\n"
        else:
            # Get full page HTML
            html_content = await page.content()
        
        result = {
            "status": "success",
            "url": page.url,
            "selector": selector or "full page",
            "html": html_content
        }
        
        if include_styles:
            # Get computed styles for elements
            styles_script = """
            () => {
                const styles = {};
                const elements = document.querySelectorAll('*');
                elements.forEach((el, index) => {
                    if (index < 100) { // Limit to first 100 elements
                        const computedStyle = window.getComputedStyle(el);
                        styles[el.tagName + '_' + index] = {
                            display: computedStyle.display,
                            position: computedStyle.position,
                            width: computedStyle.width,
                            height: computedStyle.height
                        };
                    }
                });
                return styles;
            }
            """
            styles = await page.evaluate(styles_script)
            result["styles"] = styles
        
        return json.dumps(result, indent=2 if pretty_print else None)
        
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def find_login_elements(include_html: bool = True) -> str:
    """
    Find common login form elements on the current page.
    
    Args:
        include_html: Include HTML content of found elements
    
    Returns:
        JSON string with found login elements
    """
    global page
    
    if not page:
        return json.dumps({"error": "No page loaded. Use load_page first."}, indent=2)

    try:
        # Common selectors for login elements
        login_selectors = {
            "forms": "form",
            "username_inputs": "input[type='text'], input[type='email'], input[name*='user'], input[name*='login'], input[name*='email'], input[id*='user'], input[id*='login'], input[id*='email']",
            "password_inputs": "input[type='password'], input[name*='pass'], input[id*='pass']",
            "submit_buttons": "button[type='submit'], input[type='submit'], button:has-text('login'), button:has-text('sign in'), button:has-text('submit')",
            "all_inputs": "input",
            "all_buttons": "button"
        }

        found_elements = {}

        for element_type, selector in login_selectors.items():
            try:
                elements = await page.locator(selector).all()
                element_info = []

                for i, element in enumerate(elements):
                    info = {
                        "index": i,
                        "tag": await element.evaluate("el => el.tagName.toLowerCase()"),
                        "type": await element.get_attribute("type") or "",
                        "name": await element.get_attribute("name") or "",
                        "id": await element.get_attribute("id") or "",
                        "class": await element.get_attribute("class") or "",
                        "placeholder": await element.get_attribute("placeholder") or "",
                        "value": await element.get_attribute("value") or "",
                        "visible": await element.is_visible()
                    }

                    if include_html:
                        try:
                            info["html"] = await element.evaluate("el => el.outerHTML")
                        except:
                            info["html"] = "Could not extract HTML"

                    element_info.append(info)

                found_elements[element_type] = {
                    "count": len(elements),
                    "elements": element_info
                }

            except Exception as e:
                found_elements[element_type] = {
                    "count": 0,
                    "error": str(e),
                    "elements": []
                }

        result = {
            "status": "success",
            "url": page.url,
            "found_elements": found_elements,
            "summary": {
                "total_forms": found_elements.get("forms", {}).get("count", 0),
                "total_username_inputs": found_elements.get("username_inputs", {}).get("count", 0),
                "total_password_inputs": found_elements.get("password_inputs", {}).get("count", 0),
                "total_submit_buttons": found_elements.get("submit_buttons", {}).get("count", 0),
                "total_inputs": found_elements.get("all_inputs", {}).get("count", 0),
                "total_buttons": found_elements.get("all_buttons", {}).get("count", 0)
            }
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def close_browser() -> str:
    """
    Close the browser instance.
    
    Returns:
        JSON string with close result
    """
    global playwright_instance, browser, page
    
    try:
        if page:
            await page.close()
            page = None

        if browser:
            await browser.close()
            browser = None

        if playwright_instance:
            await playwright_instance.stop()
            playwright_instance = None

        return json.dumps({"status": "success", "message": "Browser closed"}, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


if __name__ == "__main__":
    mcp.run()
