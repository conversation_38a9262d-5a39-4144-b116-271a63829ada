#!/usr/bin/env python3
"""
Run the FastMCP Playwright server.
"""

import asyncio
from playwright_mcp_server import mcp


async def main():
    """Main entry point."""
    print("Starting FastMCP Playwright server...")
    print("Available tools:")
    print("- load_page: Load a web page using Playwright")
    print("- get_dom: Get the current page's DOM content")
    print("- find_login_elements: Find common login form elements")
    print("- close_browser: Close the browser instance")
    print()
    print("Server is ready to accept connections...")
    
    # Run the server
    await mcp.run()


if __name__ == "__main__":
    asyncio.run(main())
