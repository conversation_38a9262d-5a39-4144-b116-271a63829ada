#!/usr/bin/env python3
"""
Demo script to load http://localhost:9001/login and extract DOM using FastMCP.
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path


class MCPClient:
    """Simple MCP client for testing."""
    
    def __init__(self, server_script):
        self.server_script = server_script
        self.server_process = None
        self.request_id = 0
    
    async def start_server(self):
        """Start the MCP server."""
        print("🚀 Starting FastMCP Playwright server...")
        self.server_process = subprocess.Popen(
            [sys.executable, str(self.server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a moment for server to start
        await asyncio.sleep(2)
        
        # Initialize the server
        await self._send_request("initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {
                "name": "demo-client",
                "version": "1.0.0"
            }
        })
        
        # Send initialized notification
        await self._send_notification("notifications/initialized")
        
        print("✅ Server initialized successfully!")
    
    async def _send_request(self, method, params=None):
        """Send a JSON-RPC request."""
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method
        }
        if params:
            request["params"] = params
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        # Read response
        response_line = self.server_process.stdout.readline()
        if response_line:
            return json.loads(response_line.strip())
        return None
    
    async def _send_notification(self, method, params=None):
        """Send a JSON-RPC notification."""
        notification = {
            "jsonrpc": "2.0",
            "method": method
        }
        if params:
            notification["params"] = params
        
        self.server_process.stdin.write(json.dumps(notification) + "\n")
        self.server_process.stdin.flush()
    
    async def call_tool(self, tool_name, arguments=None):
        """Call a tool."""
        params = {"name": tool_name}
        if arguments:
            params["arguments"] = arguments
        
        response = await self._send_request("tools/call", params)
        return response
    
    async def list_tools(self):
        """List available tools."""
        response = await self._send_request("tools/list")
        return response
    
    def stop_server(self):
        """Stop the MCP server."""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 Server stopped.")


async def demo_load_target():
    """Demo loading the target URL and extracting DOM."""
    
    server_script = Path(__file__).parent / "playwright_mcp_server.py"
    client = MCPClient(server_script)
    
    try:
        # Start the server
        await client.start_server()
        
        # List available tools
        print("\n📋 Listing available tools...")
        tools_response = await client.list_tools()
        if tools_response and "result" in tools_response:
            tools = tools_response["result"]["tools"]
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
        
        # Load the target page
        print(f"\n🌐 Loading target: http://localhost:9001/login")
        load_response = await client.call_tool("load_page", {
            "url": "http://localhost:9001/login",
            "headless": True,
            "timeout": 30000
        })
        
        if load_response and "result" in load_response:
            result_data = json.loads(load_response["result"]["content"][0]["text"])
            print(f"✅ Page loaded successfully!")
            print(f"   Title: {result_data.get('title', 'N/A')}")
            print(f"   URL: {result_data.get('url', 'N/A')}")
            print(f"   Status: {result_data.get('status', 'N/A')}")
        else:
            print(f"❌ Failed to load page: {load_response}")
            return
        
        # Get the DOM content
        print(f"\n📄 Extracting DOM content...")
        dom_response = await client.call_tool("get_dom", {
            "pretty_print": True
        })
        
        if dom_response and "result" in dom_response:
            dom_data = json.loads(dom_response["result"]["content"][0]["text"])
            if dom_data.get("status") == "success":
                html_content = dom_data.get("html", "")
                print(f"✅ DOM extracted successfully!")
                print(f"   HTML length: {len(html_content)} characters")
                
                # Save DOM to file
                with open("extracted_dom.html", "w", encoding="utf-8") as f:
                    f.write(html_content)
                print(f"   💾 DOM saved to: extracted_dom.html")
                
                # Show first 500 characters as preview
                print(f"\n📖 DOM Preview (first 500 chars):")
                print("-" * 50)
                print(html_content[:500])
                if len(html_content) > 500:
                    print("...")
                print("-" * 50)
            else:
                print(f"❌ Failed to extract DOM: {dom_data.get('error', 'Unknown error')}")
        else:
            print(f"❌ Failed to get DOM: {dom_response}")
        
        # Find login elements
        print(f"\n🔍 Finding login elements...")
        login_response = await client.call_tool("find_login_elements", {
            "include_html": True
        })
        
        if login_response and "result" in login_response:
            login_data = json.loads(login_response["result"]["content"][0]["text"])
            if login_data.get("status") == "success":
                summary = login_data.get("summary", {})
                print(f"✅ Login elements found!")
                print(f"   Forms: {summary.get('total_forms', 0)}")
                print(f"   Username inputs: {summary.get('total_username_inputs', 0)}")
                print(f"   Password inputs: {summary.get('total_password_inputs', 0)}")
                print(f"   Submit buttons: {summary.get('total_submit_buttons', 0)}")
                print(f"   Total inputs: {summary.get('total_inputs', 0)}")
                print(f"   Total buttons: {summary.get('total_buttons', 0)}")
                
                # Save login elements to file
                with open("login_elements.json", "w", encoding="utf-8") as f:
                    json.dump(login_data, f, indent=2)
                print(f"   💾 Login elements saved to: login_elements.json")
            else:
                print(f"❌ Failed to find login elements: {login_data.get('error', 'Unknown error')}")
        else:
            print(f"❌ Failed to find login elements: {login_response}")
        
        # Close browser
        print(f"\n🔒 Closing browser...")
        close_response = await client.call_tool("close_browser")
        if close_response and "result" in close_response:
            close_data = json.loads(close_response["result"]["content"][0]["text"])
            if close_data.get("status") == "success":
                print(f"✅ Browser closed successfully!")
            else:
                print(f"❌ Failed to close browser: {close_data.get('error', 'Unknown error')}")
        
        print(f"\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
    
    finally:
        # Clean up
        client.stop_server()


if __name__ == "__main__":
    print("🎯 FastMCP Playwright DOM Extraction Demo")
    print("=" * 50)
    print("Target URL: http://localhost:9001/login")
    print("=" * 50)
    
    asyncio.run(demo_load_target())
