#!/usr/bin/env python3
"""
测试所有路径是否正确配置的脚本
"""

import os
import sys
from pathlib import Path

def test_file_exists(file_path, description):
    """测试文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (NOT FOUND)")
        return False

def test_all_paths():
    """测试所有路径"""
    print("🔍 Testing all file paths...")
    print("=" * 50)
    
    all_good = True
    
    # 测试模板文件
    print("\n📁 Templates:")
    templates = [
        ("templates/test-dvwa.yaml", "DVWA配置文件"),
        ("templates/test-grafana.yaml", "Grafana配置文件"),
        ("templates/test-minio.yaml", "MinIO配置文件"),
        ("templates/test-connectivity-demo.yaml", "连通性测试配置文件")
    ]
    
    for file_path, desc in templates:
        if not test_file_exists(file_path, desc):
            all_good = False
    
    # 测试字典文件
    print("\n📚 Dictionaries:")
    dicts = [
        ("dicts/username.txt", "用户名字典"),
        ("dicts/password.txt", "密码字典")
    ]
    
    for file_path, desc in dicts:
        if not test_file_exists(file_path, desc):
            all_good = False
    
    # 测试测试文件
    print("\n🧪 Test files:")
    tests = [
        ("test/test_connectivity.py", "连通性测试脚本"),
        ("test/test_connectivity_accessible.py", "可访问目标测试脚本"),
        ("test/test_fastmcp_client.py", "FastMCP客户端测试")
    ]
    
    for file_path, desc in tests:
        if not test_file_exists(file_path, desc):
            all_good = False
    
    # 测试主要文件
    print("\n🎯 Main files:")
    main_files = [
        ("grafana_executor.py", "主执行文件"),
        ("pyproject.toml", "项目配置文件"),
        ("CONNECTIVITY_CHECK.md", "连通性检查文档")
    ]
    
    for file_path, desc in main_files:
        if not test_file_exists(file_path, desc):
            all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 所有文件路径都正确配置！")
        return True
    else:
        print("❌ 有些文件路径需要修复")
        return False

def test_imports():
    """测试导入是否正常工作"""
    print("\n🔧 Testing imports...")
    print("=" * 50)
    
    try:
        # 测试从test目录导入
        sys.path.append('test')
        from test_connectivity import test_connectivity
        print("✅ test_connectivity 导入成功")
        
        from test_connectivity_accessible import test_connectivity_accessible
        print("✅ test_connectivity_accessible 导入成功")
        
        # 测试主模块导入
        from grafana_executor import check_target_connectivity
        print("✅ grafana_executor 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 路径配置验证脚本")
    print("=" * 50)
    
    paths_ok = test_all_paths()
    imports_ok = test_imports()
    
    if paths_ok and imports_ok:
        print("\n🎊 所有测试通过！项目路径配置正确。")
        sys.exit(0)
    else:
        print("\n💥 有些测试失败，请检查配置。")
        sys.exit(1)
