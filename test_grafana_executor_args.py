#!/usr/bin/env python3
"""
测试 grafana_executor.py 的命令行参数功能
"""

import subprocess
import sys
import os

def test_help_option():
    """测试 --help 选项"""
    print("Testing --help option...")
    result = subprocess.run([sys.executable, "grafana_executor.py", "--help"], 
                          capture_output=True, text=True)
    assert result.returncode == 0
    assert "Grafana brute-force login tool" in result.stdout
    assert "-t TEMPLATE, --template TEMPLATE" in result.stdout
    print("✅ --help option works correctly")

def test_missing_required_arg():
    """测试缺少必需参数的情况"""
    print("Testing missing required argument...")
    result = subprocess.run([sys.executable, "grafana_executor.py"], 
                          capture_output=True, text=True)
    assert result.returncode == 2
    assert "the following arguments are required: -t/--template" in result.stderr
    print("✅ Missing required argument handled correctly")

def test_template_arg():
    """测试模板参数"""
    print("Testing template argument...")
    # 创建一个临时的测试模板文件
    test_template = "test_template.yaml"
    test_usernames = "test_usernames.txt"
    test_passwords = "test_passwords.txt"

    with open(test_template, 'w') as f:
        f.write("""
name: "Test Template"
description: "Test template for argument testing"
steps:
  - name: "Navigate to test page"
    action: "navigate"
    parameters:
      url: "http://localhost:99999"  # 使用一个不存在的端口，会快速失败
""")

    # 创建测试字典文件
    with open(test_usernames, 'w') as f:
        f.write("testuser\n")
    with open(test_passwords, 'w') as f:
        f.write("testpass\n")

    try:
        # 测试短选项
        result = subprocess.run([sys.executable, "grafana_executor.py", "-t", test_template,
                               "-u", test_usernames, "-p", test_passwords],
                              capture_output=True, text=True, timeout=15)
        # 应该会因为连接失败而退出
        assert "Target is not accessible" in result.stdout or "Connection error" in result.stdout
        print("✅ Template argument (-t) works correctly")

        # 测试长选项
        result = subprocess.run([sys.executable, "grafana_executor.py", "--template", test_template,
                               "--usernames", test_usernames, "--passwords", test_passwords],
                              capture_output=True, text=True, timeout=15)
        assert "Target is not accessible" in result.stdout or "Connection error" in result.stdout
        print("✅ Template argument (--template) works correctly")

    finally:
        # 清理测试文件
        for file in [test_template, test_usernames, test_passwords]:
            if os.path.exists(file):
                os.remove(file)

def main():
    """运行所有测试"""
    print("Running grafana_executor.py argument tests...\n")
    
    try:
        test_help_option()
        test_missing_required_arg()
        test_template_arg()
        print("\n🎉 All tests passed! Command line arguments are working correctly.")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
